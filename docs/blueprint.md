# **App Name**: Resume Tailor AI

## Core Features:

- Input Interface: User interface to input resume and job description via text fields or file uploads.
- Resume Tailoring: Generates a tailored resume by extracting and reordering relevant content from the original resume based on the job description, using a tool to determine when to rephrase or add information.
- ATS Score Simulation: Simulates an ATS score (out of 100) reflecting the resume's alignment with the job description; generates a justification for the score, highlighting key strengths and weaknesses using generative AI.
- Output Display: Displays the tailored resume, simulated ATS score, and score justification in a clear, readable format.
- Iterative Refinement: Allows the user to provide feedback or additional instructions to the AI for iterative refinement of the tailored resume.
- Text Parsing: Basic parsing of the text from resume and job description uploads

## Style Guidelines:

- Primary color: Indigo (#4B0082) to convey professionalism and competence, but avoiding overused 'corporate' blues.
- Background color: Light gray (#F0F0F0), almost white, for a clean and readable layout.
- Accent color: Teal (#008080) as a secondary color to add a touch of creativity and distinguish interactive elements from the neutral background.
- Body font: 'Inter', a sans-serif font known for its readability on screens; for the body of the tailored resume, ATS score justification, and input instructions.
- Headline font: 'Space Grotesk' sans-serif. Pair with 'Inter' body font for the titles of the page or page sections, where design aesthetics are important. (Note: Currently only Google Fonts are supported.)
- Simple, professional icons for UI elements such as file upload, feedback submission, and result sections, promoting intuitive navigation.
- Clean, structured layout with clear divisions between input, output, and feedback sections to prevent cognitive overload and facilitate efficient task completion.