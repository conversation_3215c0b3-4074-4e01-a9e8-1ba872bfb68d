# AI Output Samples for Markdown Renderer

This file contains examples of different types of AI-generated content that the markdown renderer can handle.

## Sample 1: Structured Analysis (Full Markdown)

```markdown
# ATS Score Analysis: 78/100

## Key Strengths
- **Keyword Matching**: Strong alignment with job requirements (85% match)
- **Technical Skills**: Comprehensive coverage of required technologies
- **Experience Relevance**: 5+ years in relevant domain

## Areas for Improvement
1. **Quantifiable Achievements**: Add more specific metrics and numbers
2. **Industry Keywords**: Include more sector-specific terminology  
3. **Soft Skills**: Better highlight leadership and communication abilities

### Specific Recommendations
- Incorporate project outcomes with *measurable results*
- Use action verbs like "implemented," "optimized," and "delivered"
- Add relevant certifications and training

> **Note**: This analysis is based on current ATS algorithms and industry best practices.

---

**Overall Assessment**: Good foundation with targeted improvement opportunities.
```

## Sample 2: Plain Text with Structure (Auto-converted)

```
ATS SCORE ANALYSIS

Key Strengths:
- Strong technical background
- Relevant industry experience
- Good educational foundation

Areas for Improvement:
1. Add more quantifiable achievements
2. Include industry-specific keywords
3. Improve formatting consistency

Recommendations:
Use more action verbs in your experience section. Consider adding specific metrics to demonstrate impact. The resume shows good potential but needs refinement to pass ATS filters effectively.
```

## Sample 3: Mixed Format (Partially Structured)

```
**ATS Score: 72/100**

STRENGTHS:
- Technical skills match job requirements well
- Experience level is appropriate
- Education background is solid

WEAKNESSES:
The resume lacks specific achievements and metrics. Consider adding:
1. Quantifiable results from previous roles
2. More industry-specific terminology
3. Better keyword optimization

Overall, this is a decent resume that needs targeted improvements to increase ATS compatibility.
```

## Sample 4: Simple Plain Text (Fallback Mode)

```
Your resume scores 65 out of 100 for this position. The main strengths are your technical background and relevant experience. However, you should add more specific achievements with numbers and metrics. Also consider including more keywords from the job description. The formatting looks good and should work well with most ATS systems.
```

## How the Renderer Handles Each Sample

1. **Sample 1**: Renders with full markdown formatting - headers, lists, emphasis, blockquotes
2. **Sample 2**: Auto-detects structure and converts to markdown - headers become `##`, lists get proper formatting
3. **Sample 3**: Preserves existing markdown while enhancing plain text sections
4. **Sample 4**: Falls back to plain text with proper styling when no structure is detected

## Benefits

- **Consistent Styling**: All content follows the app's design system
- **Improved Readability**: Structured content is easier to scan and understand
- **Flexible Handling**: Works with various AI output formats
- **Graceful Degradation**: Falls back to plain text when needed
- **Dark Mode Support**: Automatically adapts to theme changes
