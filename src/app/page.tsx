"use client";

import * as React from "react";
import { zod<PERSON>esolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { Clipboard, FileText, Info, Loader2, Spa<PERSON>les, Wand2 } from "lucide-react";
import { tailorResume, TailorResumeOutput } from "@/ai/flows/tailor-resume";
import { refineTailoredResume } from "@/ai/flows/refine-tailored-resume";
import { simulateATSScore, SimulateATSScoreOutput } from "@/ai/flows/simulate-ats-score";
import { useToast } from "@/hooks/use-toast";
import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Textarea } from "@/components/ui/textarea";
import { Header } from "@/components/layout/header";
import { Ta<PERSON>, TabsContent, <PERSON>bsList, TabsTrigger } from "@/components/ui/tabs";
import { Skeleton } from "@/components/ui/skeleton";
import { ScrollArea } from "@/components/ui/scroll-area";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Separator } from "@/components/ui/separator";
import { MarkdownRenderer } from "@/components/ui/markdown-renderer";

const formSchema = z.object({
  resume: z.string().min(100, { message: "Please paste your full resume." }),
  jobDescription: z.string().min(50, { message: "Please paste the full job description." }),
});

const refinementSchema = z.object({
  feedback: z.string().min(10, { message: "Please provide some feedback." }),
});

export default function ResumeTailorPage() {
  const { toast } = useToast();
  const [isLoading, setIsLoading] = React.useState(false);
  const [isRefining, setIsRefining] = React.useState(false);
  const [isSampleLoading, setIsSampleLoading] = React.useState(false);
  const [output, setOutput] = React.useState<TailorResumeOutput | null>(null);
  const [initialScore, setInitialScore] = React.useState<SimulateATSScoreOutput | null>(null);
  const [originalInputs, setOriginalInputs] = React.useState<{ resume: string; jobDescription: string } | null>(null);

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      resume: "",
      jobDescription: "",
    },
  });

  const refinementForm = useForm<z.infer<typeof refinementSchema>>({
    resolver: zodResolver(refinementSchema),
    defaultValues: { feedback: "" },
  });

  const loadSampleData = async () => {
    setIsSampleLoading(true);
    try {
      const [resumeRes, jobDescRes] = await Promise.all([
        fetch('/sample-resume.txt'),
        fetch('/sample-job-description.txt')
      ]);
      if (!resumeRes.ok || !jobDescRes.ok) {
        throw new Error('Failed to fetch sample data');
      }
      const [resumeText, jobDescText] = await Promise.all([
        resumeRes.text(),
        jobDescRes.text()
      ]);
      form.setValue('resume', resumeText, { shouldValidate: true });
      form.setValue('jobDescription', jobDescText, { shouldValidate: true });
    } catch (error) {
      console.error("Failed to load sample data", error);
      toast({
        variant: "destructive",
        title: "Error",
        description: "Could not load sample data. Please try again.",
      });
    } finally {
      setIsSampleLoading(false);
    }
  };


  const handleTailorSubmit = async (values: z.infer<typeof formSchema>) => {
    setIsLoading(true);
    setOutput(null);
    setInitialScore(null);
    setOriginalInputs(values);
    try {
      const currentDate = new Date().toLocaleDateString();
      const initialScoreResult = await simulateATSScore({
        tailoredResume: values.resume,
        jobDescription: values.jobDescription,
        currentDate: currentDate,
      });
      setInitialScore(initialScoreResult);

      const result = await tailorResume({ ...values, currentDate: currentDate });
      setOutput(result);
    } catch (error) {
      console.error("Error tailoring resume:", error);
      const isRateLimitError = error instanceof Error && error.message.startsWith('Too many requests');
      toast({
        variant: "destructive",
        title: isRateLimitError ? 'Rate Limit Exceeded' : "An error occurred",
        description: isRateLimitError ? (error as Error).message : "Failed to tailor resume. Please try again.",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleRefineSubmit = async (values: z.infer<typeof refinementSchema>) => {
    if (!originalInputs || !output) return;
    setIsRefining(true);
    try {
      setInitialScore({
        atsScore: output.atsScore,
        justification: output.scoreJustification,
      });

      const result = await refineTailoredResume({
        originalResume: originalInputs.resume,
        jobDescription: originalInputs.jobDescription,
        tailoredResume: output.tailoredResume,
        feedback: values.feedback,
        currentDate: new Date().toLocaleDateString(),
      });
      setOutput({
        tailoredResume: result.refinedResume,
        atsScore: result.atsScore,
        scoreJustification: result.scoreJustification,
      });
      refinementForm.reset();
    } catch (error) {
      console.error("Error refining resume:", error);
      const isRateLimitError = error instanceof Error && error.message.startsWith('Too many requests');
      toast({
        variant: "destructive",
        title: isRateLimitError ? 'Rate Limit Exceeded' : "An error occurred",
        description: isRateLimitError ? (error as Error).message : "Failed to refine resume. Please try again.",
      });
    } finally {
      setIsRefining(false);
    }
  };

  const copyToClipboard = () => {
    if (output?.tailoredResume) {
      navigator.clipboard.writeText(output.tailoredResume);
      toast({ title: "Copied to clipboard!" });
    }
  };

  const getScoreColor = (score: number) => {
    if (score >= 80) return "text-chart-2";
    if (score >= 50) return "text-chart-4";
    return "text-destructive";
  };

  return (
    <div className="flex flex-col min-h-screen bg-background text-foreground">
      <Header />
      <main className="flex-1 container mx-auto px-4 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 items-start">
          <Card className="shadow-lg">
            <CardHeader>
              <div className="flex justify-between items-start">
                <div className="space-y-1.5">
                  <CardTitle className="font-headline text-2xl flex items-center gap-2">
                    <FileText className="text-primary" />
                    Input Details
                  </CardTitle>
                  <CardDescription>
                    Paste your resume and the job description below to get started.
                  </CardDescription>
                </div>
                <Button variant="outline" size="sm" onClick={loadSampleData} disabled={isSampleLoading}>
                  {isSampleLoading ? (
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  ) : null}
                  Load Sample
                </Button>
              </div>
            </CardHeader>
            <Form {...form}>
              <form onSubmit={form.handleSubmit(handleTailorSubmit)}>
                <CardContent className="space-y-6">
                  <FormField
                    control={form.control}
                    name="resume"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Your Resume</FormLabel>
                        <FormControl>
                          <Textarea
                            placeholder="Paste your full resume here..."
                            className="min-h-[250px] font-mono text-sm"
                            {...field}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={form.control}
                    name="jobDescription"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Job Description</FormLabel>
                        <FormControl>
                          <Textarea
                            placeholder="Paste the job description here..."
                            className="min-h-[200px] text-sm"
                            {...field}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </CardContent>
                <CardFooter>
                  <Button type="submit" disabled={isLoading} className="w-full">
                    {isLoading ? (
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    ) : (
                      <Sparkles className="mr-2 h-4 w-4" />
                    )}
                    Tailor My Resume
                  </Button>
                </CardFooter>
              </form>
            </Form>
          </Card>

          <div className="lg:sticky top-8 space-y-8">
            <Card className="shadow-lg">
              <CardHeader>
                <CardTitle className="font-headline text-2xl">Your Tailored Resume</CardTitle>
                <CardDescription>
                  AI-powered analysis and refinement of your resume.
                </CardDescription>
              </CardHeader>
              <CardContent>
                {isLoading && (
                  <div className="space-y-4">
                    <Skeleton className="h-8 w-1/2" />
                    <div className="space-y-2">
                      <Skeleton className="h-4 w-full" />
                      <Skeleton className="h-4 w-full" />
                      <Skeleton className="h-4 w-3/4" />
                    </div>
                    <div className="flex justify-center pt-4">
                      <Loader2 className="h-12 w-12 animate-spin text-primary" />
                    </div>
                  </div>
                )}
                {!isLoading && !output && (
                  <div className="text-center py-16 text-muted-foreground">
                    <FileText size={48} className="mx-auto" />
                    <p className="mt-4">Your results will appear here.</p>
                  </div>
                )}
                {output && (
                  <Tabs defaultValue="resume">
                    <TabsList className="grid w-full grid-cols-2">
                      <TabsTrigger value="resume">Tailored Resume</TabsTrigger>
                      <TabsTrigger value="analysis">ATS Analysis</TabsTrigger>
                    </TabsList>
                    <TabsContent value="resume" className="mt-4">
                      <Card>
                        <CardHeader className="flex flex-row items-center justify-between">
                          <CardTitle className="text-lg">Your New Resume</CardTitle>
                          <Button variant="ghost" size="icon" onClick={copyToClipboard}>
                            <Clipboard className="h-4 w-4" />
                          </Button>
                        </CardHeader>
                        <CardContent>
                          <ScrollArea className="h-[400px] w-full rounded-md border p-4">
                            <pre className="whitespace-pre-wrap text-sm font-mono">{output.tailoredResume}</pre>
                          </ScrollArea>
                        </CardContent>
                      </Card>
                    </TabsContent>
                    <TabsContent value="analysis" className="mt-4 space-y-4">
                      {initialScore && output ? (
                        <>
                          <Card>
                            <CardHeader>
                              <CardTitle className="text-lg">ATS Score Comparison</CardTitle>
                              <p className="text-sm text-muted-foreground mt-1">
                                * Simulated and simplified for demonstration purposes
                              </p>
                            </CardHeader>
                            <CardContent>
                              <Table>
                                <TableHeader>
                                  <TableRow>
                                    <TableHead className="w-[120px]"></TableHead>
                                    <TableHead className="text-center font-headline text-lg">Before</TableHead>
                                    <TableHead className="text-center font-headline text-lg">After</TableHead>
                                  </TableRow>
                                </TableHeader>
                                <TableBody>
                                  <TableRow>
                                    <TableCell className="font-semibold align-middle">
                                      Score
                                    </TableCell>
                                    <TableCell className={`text-center text-4xl font-bold font-headline ${getScoreColor(initialScore.atsScore)}`}>
                                      {Math.round(initialScore.atsScore)}
                                    </TableCell>
                                    <TableCell className={`text-center text-4xl font-bold font-headline ${getScoreColor(output.atsScore)}`}>
                                      {Math.round(output.atsScore)}
                                    </TableCell>
                                  </TableRow>
                                </TableBody>
                              </Table>
                            </CardContent>
                          </Card>
                          <Card>
                            <CardHeader>
                              <CardTitle className="text-lg">Justification Analysis</CardTitle>
                            </CardHeader>
                            <CardContent className="space-y-6">
                              <div>
                                <h4 className="font-headline text-base font-semibold mb-2">Before</h4>
                                <MarkdownRenderer
                                  content={initialScore.justification}
                                  autoDetect={true}
                                  fallbackToPlainText={true}
                                />
                              </div>
                              <Separator/>
                              <div>
                                <h4 className="font-headline text-base font-semibold mb-2">After</h4>
                                <MarkdownRenderer
                                  content={output.scoreJustification}
                                  autoDetect={true}
                                  fallbackToPlainText={true}
                                />
                              </div>
                            </CardContent>
                          </Card>
                        </>
                      ) : null}
                    </TabsContent>
                  </Tabs>
                )}
              </CardContent>
              {output && (
                <CardFooter>
                  <div className="flex items-start text-xs text-muted-foreground gap-2">
                    <Info className="h-4 w-4 shrink-0 mt-0.5" />
                    <p className="italic">
                      Disclaimer: The tailored resume and ATS score are AI-generated. Review and edit for accuracy and personality before sending.
                    </p>
                  </div>
                </CardFooter>
              )}
            </Card>

            {output && (
              <Card className="shadow-lg">
                <CardHeader>
                  <CardTitle className="font-headline text-xl flex items-center gap-2">
                    <Wand2 className="text-accent"/>
                    Iterative Refinement
                  </CardTitle>
                  <CardDescription>
                    Provide feedback or instructions to further refine your resume.
                  </CardDescription>
                </CardHeader>
                <Form {...refinementForm}>
                  <form onSubmit={refinementForm.handleSubmit(handleRefineSubmit)}>
                    <CardContent>
                      <FormField
                        control={refinementForm.control}
                        name="feedback"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Your Feedback</FormLabel>
                            <FormControl>
                              <Textarea
                                placeholder="e.g., 'Emphasize my project management skills more' or 'Make the summary more concise.'"
                                className="min-h-[100px]"
                                {...field}
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </CardContent>
                    <CardFooter>
                      <Button type="submit" variant="outline" disabled={isRefining} className="w-full">
                         {isRefining ? (
                          <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                        ) : (
                          <Wand2 className="mr-2 h-4 w-4" />
                        )}
                        Refine Resume
                      </Button>
                    </CardFooter>
                  </form>
                </Form>
              </Card>
            )}
          </div>
        </div>
      </main>
    </div>
  );
}
