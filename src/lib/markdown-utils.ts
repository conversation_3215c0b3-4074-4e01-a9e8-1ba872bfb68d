/**
 * Utility functions for markdown detection and processing
 */

/**
 * Detects if a string contains markdown formatting
 * @param content - The string to check for markdown
 * @returns boolean indicating if markdown formatting is detected
 */
export function hasMarkdownFormatting(content: string): boolean {
  if (!content || typeof content !== 'string') {
    return false
  }

  // Common markdown patterns to detect
  const markdownPatterns = [
    /^#{1,6}\s+/m,           // Headers (# ## ###)
    /\*\*[^*]+\*\*/,         // Bold text (**text**)
    /\*[^*]+\*/,             // Italic text (*text*)
    /^[-*+]\s+/m,            // Unordered lists (- * +)
    /^\d+\.\s+/m,            // Ordered lists (1. 2.)
    /^>\s+/m,                // Blockquotes (>)
    /^---+$/m,               // Horizontal rules (---)
    /`[^`]+`/,               // Inline code (`code`)
    /```[\s\S]*?```/,        // Code blocks (```)
    /\[[^\]]+\]\([^)]+\)/,   // Links ([text](url))
  ]

  return markdownPatterns.some(pattern => pattern.test(content))
}

/**
 * Estimates the complexity of markdown content
 * @param content - The markdown string to analyze
 * @returns number indicating complexity (0-10 scale)
 */
export function getMarkdownComplexity(content: string): number {
  if (!content || typeof content !== 'string') {
    return 0
  }

  let complexity = 0

  // Count different markdown elements
  const elements = {
    headers: (content.match(/^#{1,6}\s+/gm) || []).length,
    bold: (content.match(/\*\*[^*]+\*\*/g) || []).length,
    italic: (content.match(/\*[^*]+\*/g) || []).length,
    lists: (content.match(/^[-*+\d+\.]\s+/gm) || []).length,
    blockquotes: (content.match(/^>\s+/gm) || []).length,
    codeBlocks: (content.match(/```[\s\S]*?```/g) || []).length,
    inlineCode: (content.match(/`[^`]+`/g) || []).length,
    links: (content.match(/\[[^\]]+\]\([^)]+\)/g) || []).length,
    horizontalRules: (content.match(/^---+$/gm) || []).length,
  }

  // Weight different elements
  complexity += elements.headers * 1
  complexity += elements.bold * 0.5
  complexity += elements.italic * 0.3
  complexity += elements.lists * 0.8
  complexity += elements.blockquotes * 1.2
  complexity += elements.codeBlocks * 2
  complexity += elements.inlineCode * 0.4
  complexity += elements.links * 1
  complexity += elements.horizontalRules * 0.5

  // Normalize to 0-10 scale
  return Math.min(Math.round(complexity / 2), 10)
}

/**
 * Sanitizes markdown content by removing potentially problematic elements
 * @param content - The markdown string to sanitize
 * @returns sanitized markdown string
 */
export function sanitizeMarkdown(content: string): string {
  if (!content || typeof content !== 'string') {
    return ''
  }

  // Remove HTML tags (basic sanitization)
  let sanitized = content.replace(/<[^>]*>/g, '')
  
  // Remove potentially problematic markdown elements
  // Remove image syntax ![alt](url)
  sanitized = sanitized.replace(/!\[[^\]]*\]\([^)]*\)/g, '')
  
  // Limit header levels to h1-h3 for consistency
  sanitized = sanitized.replace(/^#{4,6}\s+/gm, '### ')
  
  return sanitized.trim()
}

/**
 * Converts plain text to basic markdown if it appears to be structured
 * @param content - Plain text content
 * @returns markdown-formatted string if structure is detected, otherwise original content
 */
export function autoMarkdownify(content: string): string {
  if (!content || typeof content !== 'string') {
    return content
  }

  // If already has markdown, return as-is
  if (hasMarkdownFormatting(content)) {
    return content
  }

  let processed = content

  // Convert lines that look like headers (ALL CAPS or Title Case at start of line)
  processed = processed.replace(/^([A-Z][A-Z\s]{3,}):?\s*$/gm, '## $1')
  processed = processed.replace(/^([A-Z][a-z\s]+):?\s*$/gm, '### $1')

  // Convert bullet points (lines starting with - or *)
  processed = processed.replace(/^[-*]\s+/gm, '- ')

  // Convert numbered lists
  processed = processed.replace(/^(\d+)[\.)]\s+/gm, '$1. ')

  // Emphasize words in quotes
  processed = processed.replace(/"([^"]+)"/g, '**$1**')

  return processed
}
