// src/ai/flows/justify-ats-score.ts
'use server';

/**
 * @fileOverview Generates a justification for the simulated ATS score, highlighting key strengths and weaknesses.
 *
 * - justifyATSScore - A function that generates the ATS score justification.
 * - JustifyATSScoreInput - The input type for the justifyATSScore function.
 * - JustifyATSScoreOutput - The return type for the justifyATSScore function.
 */

import {ai} from '@/ai/genkit';
import {z} from 'genkit';

const JustifyATSScoreInputSchema = z.object({
  resume: z.string().describe('The tailored resume text.'),
  jobDescription: z.string().describe('The job description text.'),
  atsScore: z.number().describe('The simulated ATS score (0-100).'),
  currentDate: z.string().describe('The current date to interpret date ranges like "2020 - Present".'),
});
export type JustifyATSScoreInput = z.infer<typeof JustifyATSScoreInputSchema>;

const JustifyATSScoreOutputSchema = z.object({
  justification: z.string().describe('A justification for the ATS score, highlighting strengths and weaknesses.'),
});
export type JustifyATSScoreOutput = z.infer<typeof JustifyATSScoreOutputSchema>;

export async function justifyATSScore(input: JustifyATSScoreInput): Promise<JustifyATSScoreOutput> {
  return justifyATSScoreFlow(input);
}

const prompt = ai.definePrompt({
  name: 'justifyATSScorePrompt',
  input: {schema: JustifyATSScoreInputSchema},
  output: {schema: JustifyATSScoreOutputSchema},
  prompt: `You are a senior career strategist and ATS optimization expert with deep understanding of how Applicant Tracking Systems evaluate resumes and what makes candidates successful.

Current Date: {{{currentDate}}} (Use this to correctly interpret relative dates like "Present")

Your task is to provide a comprehensive justification for the given ATS score, offering strategic insights and actionable recommendations.

**Analysis Framework:**
1. **Keyword Analysis** - Identify matched and missing critical keywords
2. **Skills Assessment** - Evaluate technical and soft skills alignment
3. **Experience Evaluation** - Assess relevance, progression, and quantifiable achievements
4. **Structural Review** - Check format, organization, and ATS compatibility
5. **Competitive Positioning** - How this resume stands against typical candidates

**Justification Structure:**
- **Score Rationale**: Clear explanation of why this specific score was assigned
- **Key Strengths**: Top 3-4 strongest alignment points with specific examples
- **Critical Gaps**: Most important areas needing improvement
- **Strategic Recommendations**: Prioritized, actionable steps to improve score
- **Market Context**: How competitive this resume is in the current job market

Resume:
---
{{{resume}}}
---

Job Description:
---
{{{jobDescription}}}
---

ATS Score: {{atsScore}}/100

Provide a detailed, strategic justification that helps the candidate understand their position and next steps for optimization.`,
});

const justifyATSScoreFlow = ai.defineFlow(
  {
    name: 'justifyATSScoreFlow',
    inputSchema: JustifyATSScoreInputSchema,
    outputSchema: JustifyATSScoreOutputSchema,
  },
  async input => {
    const {output} = await prompt(input);
    return output!;
  }
);
