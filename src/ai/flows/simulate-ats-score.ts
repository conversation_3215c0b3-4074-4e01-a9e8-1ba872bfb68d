// This is an auto-generated file from Firebase Studio.

'use server';

/**
 * @fileOverview Simulates an Applicant Tracking System (ATS) score for a tailored resume based on a job description.
 *
 * - simulateATSScore - A function that takes a tailored resume and a job description as input and returns a simulated ATS score and justification.
 * - SimulateATSScoreInput - The input type for the simulateATSScore function.
 * - SimulateATSScoreOutput - The return type for the simulateATSScore function.
 */

import {ai} from '@/ai/genkit';
import {z} from 'genkit';
import { checkRateLimit } from '@/lib/rate-limiter';

const SimulateATSScoreInputSchema = z.object({
  tailoredResume: z.string().describe('The tailored resume to be scored.'),
  jobDescription: z.string().describe('The job description to compare the resume against.'),
  currentDate: z.string().describe('The current date to interpret date ranges like "2020 - Present".'),
});
export type SimulateATSScoreInput = z.infer<typeof SimulateATSScoreInputSchema>;

const SimulateATSScoreOutputSchema = z.object({
  atsScore: z.number().describe('The simulated ATS score (out of 100).'),
  justification: z.string().describe('A brief justification for the simulated ATS score.'),
});
export type SimulateATSScoreOutput = z.infer<typeof SimulateATSScoreOutputSchema>;

export async function simulateATSScore(input: SimulateATSScoreInput): Promise<SimulateATSScoreOutput> {
  await checkRateLimit();
  return simulateATSScoreFlow(input);
}

const prompt = ai.definePrompt({
  name: 'simulateATSScorePrompt',
  input: {schema: SimulateATSScoreInputSchema},
  output: {schema: SimulateATSScoreOutputSchema},
  prompt: `You are a senior ATS specialist and resume optimization expert with extensive knowledge of how modern Applicant Tracking Systems evaluate and score resumes.

Current Date: {{{currentDate}}} (Use this to correctly interpret relative dates like "Present")

Your task is to simulate an accurate ATS score (0-100) for the tailored resume based on its alignment with the job description. Consider these critical ATS evaluation factors:

**Scoring Criteria:**
1. **Keyword Matching (30%)** - Exact and semantic matches with job requirements
2. **Skills Alignment (25%)** - Technical and soft skills relevance and depth
3. **Experience Relevance (25%)** - Years of experience, role progression, and industry fit
4. **Education & Certifications (10%)** - Required qualifications and credentials
5. **Format Compatibility (10%)** - ATS-friendly structure and readability

**Scoring Guidelines:**
- 90-100: Exceptional match, likely to pass initial screening
- 80-89: Strong candidate, good alignment with most requirements
- 70-79: Moderate fit, some gaps but viable candidate
- 60-69: Below average, significant improvements needed
- Below 60: Poor match, major alignment issues

Tailored Resume:
---
{{{tailoredResume}}}
---

Job Description:
---
{{{jobDescription}}}
---

Provide a detailed justification that includes:
- Specific keyword matches found
- Skills alignment assessment
- Experience relevance evaluation
- Areas of strength and weakness
- Actionable recommendations for improvement

Output the ATS score and comprehensive justification as a JSON object.`,
});

const simulateATSScoreFlow = ai.defineFlow(
  {
    name: 'simulateATSScoreFlow',
    inputSchema: SimulateATSScoreInputSchema,
    outputSchema: SimulateATSScoreOutputSchema,
  },
  async input => {
    const {output} = await prompt(input);
    return output!;
  }
);
