import { Briefcase } from "lucide-react";
import { ThemeSwitcher } from "../theme-switcher";

export function Header() {
  return (
    <header className="py-6 border-b">
      <div className="container mx-auto px-4 flex justify-between items-center">
        <div>
          <div className="flex items-center gap-3">
            <div className="p-2 bg-primary/10 rounded-lg">
              <Briefcase className="h-6 w-6 text-primary" />
            </div>
            <h1 className="text-3xl font-bold font-headline text-foreground">
              Resume Tailor
            </h1>
          </div>
          <p className="mt-2 text-muted-foreground">
            Tailor your resume to any job description.
          </p>
        </div>
        <ThemeSwitcher />
      </div>
    </header>
  );
}
