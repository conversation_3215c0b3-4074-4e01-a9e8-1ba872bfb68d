import React from 'react'
import { MarkdownRenderer } from './markdown-renderer'

// Example markdown content that might come from AI justifications
const sampleMarkdownContent = `
# ATS Score Analysis

## Key Strengths
- **Keyword Matching**: Strong alignment with job requirements (85% match)
- **Technical Skills**: Comprehensive coverage of required technologies
- **Experience Relevance**: 5+ years in relevant domain

## Areas for Improvement
1. **Quantifiable Achievements**: Add more specific metrics and numbers
2. **Industry Keywords**: Include more sector-specific terminology
3. **Soft Skills**: Better highlight leadership and communication abilities

### Recommendations
- Incorporate specific project outcomes with *measurable results*
- Use action verbs like "implemented," "optimized," and "delivered"
- Add relevant certifications and training

> **Note**: This analysis is based on current ATS algorithms and industry best practices.

---

**Overall Score**: 78/100 - Good alignment with room for targeted improvements.
`

// Test component to demonstrate markdown rendering
export function MarkdownRendererTest() {
  return (
    <div className="max-w-2xl mx-auto p-6 space-y-6">
      <h1 className="text-2xl font-bold">Markdown Renderer Test</h1>
      
      <div className="border rounded-lg p-4">
        <h2 className="text-lg font-semibold mb-4">Sample AI Justification (Markdown)</h2>
        <MarkdownRenderer content={sampleMarkdownContent} />
      </div>
      
      <div className="border rounded-lg p-4">
        <h2 className="text-lg font-semibold mb-4">Plain Text Comparison</h2>
        <div className="text-sm text-muted-foreground whitespace-pre-wrap">
          {sampleMarkdownContent}
        </div>
      </div>
    </div>
  )
}
