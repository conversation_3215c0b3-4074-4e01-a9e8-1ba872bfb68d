import React from 'react'
import ReactMarkdown from 'react-markdown'
import { cn } from '@/lib/utils'
import { hasMarkdownFormatting, sanitizeMarkdown, autoMarkdownify } from '@/lib/markdown-utils'

interface MarkdownRendererProps {
  content: string
  className?: string
  autoDetect?: boolean // Whether to auto-convert plain text to markdown
  fallbackToPlainText?: boolean // Whether to fall back to plain text if no markdown detected
}

export function MarkdownRenderer({
  content,
  className,
  autoDetect = true,
  fallbackToPlainText = false
}: MarkdownRendererProps) {
  // Early return for empty content
  if (!content || typeof content !== 'string') {
    return null
  }

  // Sanitize the content
  const sanitizedContent = sanitizeMarkdown(content)

  // Determine if we should render as markdown
  const hasMarkdown = hasMarkdownFormatting(sanitizedContent)
  const processedContent = autoDetect && !hasMarkdown
    ? autoMarkdownify(sanitizedContent)
    : sanitizedContent

  // If no markdown detected and fallback is enabled, render as plain text
  if (!hasMarkdownFormatting(processedContent) && fallbackToPlainText) {
    return (
      <div className={cn('text-sm text-muted-foreground leading-relaxed whitespace-pre-wrap', className)}>
        {sanitizedContent}
      </div>
    )
  }
  return (
    <div className={cn('prose prose-sm max-w-none dark:prose-invert', className)}>
      <ReactMarkdown
        components={{
          // Customize heading styles
          h1: ({ children }) => (
            <h1 className="text-lg font-semibold text-foreground mb-3 mt-4 first:mt-0">
              {children}
            </h1>
          ),
          h2: ({ children }) => (
            <h2 className="text-base font-semibold text-foreground mb-2 mt-3 first:mt-0">
              {children}
            </h2>
          ),
          h3: ({ children }) => (
            <h3 className="text-sm font-semibold text-foreground mb-2 mt-3 first:mt-0">
              {children}
            </h3>
          ),
          // Customize paragraph styles
          p: ({ children }) => (
            <p className="text-sm text-muted-foreground mb-3 leading-relaxed">
              {children}
            </p>
          ),
          // Customize list styles
          ul: ({ children }) => (
            <ul className="text-sm text-muted-foreground mb-3 pl-4 space-y-1">
              {children}
            </ul>
          ),
          ol: ({ children }) => (
            <ol className="text-sm text-muted-foreground mb-3 pl-4 space-y-1">
              {children}
            </ol>
          ),
          li: ({ children }) => (
            <li className="leading-relaxed">
              {children}
            </li>
          ),
          // Customize strong/bold text
          strong: ({ children }) => (
            <strong className="font-semibold text-foreground">
              {children}
            </strong>
          ),
          // Customize emphasis/italic text
          em: ({ children }) => (
            <em className="italic text-foreground">
              {children}
            </em>
          ),
          // Customize code blocks
          code: ({ children, className }) => {
            const isInline = !className
            if (isInline) {
              return (
                <code className="bg-muted px-1.5 py-0.5 rounded text-xs font-mono text-foreground">
                  {children}
                </code>
              )
            }
            return (
              <pre className="bg-muted p-3 rounded-md overflow-x-auto mb-3">
                <code className="text-xs font-mono text-foreground">
                  {children}
                </code>
              </pre>
            )
          },
          // Customize blockquotes
          blockquote: ({ children }) => (
            <blockquote className="border-l-4 border-muted-foreground/20 pl-4 italic text-muted-foreground mb-3">
              {children}
            </blockquote>
          ),
          // Customize horizontal rules
          hr: () => (
            <hr className="border-border my-4" />
          ),
        }}
      >
        {processedContent}
      </ReactMarkdown>
    </div>
  )
}
