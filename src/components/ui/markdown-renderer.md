# Markdown Renderer Component

## Overview
The `MarkdownRenderer` component is designed to render markdown-formatted text from AI-generated justifications and analysis. It provides consistent styling that matches the application's design system.

## Features
- **Tailwind CSS Integration**: Uses Tailwind's typography plugin for consistent prose styling
- **Dark Mode Support**: Automatically adapts to light/dark themes
- **Custom Component Styling**: Overrides default markdown elements with application-specific styles
- **Responsive Design**: Works well across different screen sizes

## Usage

```tsx
import { MarkdownRenderer } from '@/components/ui/markdown-renderer'

// Basic usage
<MarkdownRenderer content={markdownString} />

// With custom className
<MarkdownRenderer 
  content={markdownString} 
  className="custom-styles" 
/>
```

## Supported Markdown Elements

### Headers
- `# H1` - Large, prominent headings
- `## H2` - Section headings  
- `### H3` - Subsection headings

### Text Formatting
- `**bold text**` - Bold/strong emphasis
- `*italic text*` - Italic/emphasis
- `inline code` - Inline code snippets

### Lists
- Unordered lists with `- item`
- Ordered lists with `1. item`
- Proper spacing and indentation

### Other Elements
- `> blockquotes` - For important notes or quotes
- `---` - Horizontal rules for section separation
- Code blocks with syntax highlighting

## Styling Customization

The component uses a custom styling approach that:
- Maintains consistency with the app's design tokens
- Provides proper contrast in both light and dark modes
- Uses appropriate font sizes and spacing for readability
- Integrates seamlessly with existing UI components

## AI Integration

This component is specifically designed to handle AI-generated content that may include:
- Structured analysis with headers and sections
- Bullet points for recommendations
- Emphasis on key terms and metrics
- Code snippets for technical suggestions
- Blockquotes for important notes

## Example AI Output

```markdown
# ATS Score Analysis

## Key Strengths
- **Keyword Matching**: 85% alignment with job requirements
- **Technical Skills**: Comprehensive coverage of required technologies

## Recommendations
1. Add more *quantifiable achievements*
2. Include industry-specific terminology
3. Highlight leadership experience

> **Note**: This analysis is based on current ATS algorithms.
```

This would render as properly formatted, styled content that's easy to read and understand.
